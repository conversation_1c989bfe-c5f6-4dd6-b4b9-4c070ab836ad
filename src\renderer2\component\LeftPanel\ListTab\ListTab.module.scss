.listTab{
    position: absolute;
    inset: 0;
    display: flex;
    flex-direction: column;
    // border-style: solid;
    background-color: #191a20;
    
    &.dimListTab{
        .topSectionBtnLeft {
            opacity: 0.3;
            transition: opacity 0.3s ease-in-out;
        }
        .titleSection {
            opacity: 0.3;
            transition: opacity 0.3s ease-in-out;
        }
        .filterSection {
            opacity: 0.3;
            transition: opacity 0.3s ease-in-out;
        }
        .listSection {
            .searchLabel {
                opacity: 0.3;
                transition: opacity 0.3s ease-in-out;
            }
            .searchItemContainerMain {
                .searchItemContainer {
                    opacity: 0.3;
                    transition: opacity 0.3s ease-in-out;
                    &.selectedSearchItem {
                        opacity: 1;
                    }
                }
            }
        }

        &:hover{
            .topSectionBtnLeft {
                opacity: 1;
            }
            .titleSection {
                opacity: 1;
            }
            .filterSection {
                opacity: 1;
            }
            .listSection {
                .searchLabel {
                    opacity: 1;
                }
                .searchItemContainerMain {
                    .searchItemContainer {
                        opacity: 1;
                        &.selectedSearchItem {
                            opacity: 1;
                        }
                    }
                }
            }
        }
    }
    
    .title{
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: -0.64px;
        text-align: left;
        text-transform: uppercase;
    }
    .quoting{
        color: #32ccff;
    }
    .purchasing{
        color: #ffe352;
    }
    .instantPriceSearch{
        color: #32ff6c;
    }
    .order{
        color: #ff8c4c;
    }
    .deletedItems {
        color: #fff;
    }
    .searchResults {
        color: #fff;
    }
    .filterSection{
        width: 100%;
        display: flex;
        gap: 8px;
        padding: 16px;
        .filterSectionLeft{
            width: 48.39%;
            height: 36px;
            flex-grow: 1;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 8px 10px 8px 16px;
            border-radius: 500px;
            background-color: rgba(255, 255, 255, 0.04);
        }
        .filterSectionRight{
            width: 51.61%;
            height: 36px;
            flex-grow: 1;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 8px 10px 8px 16px;
            border-radius: 500px;
            background-color: rgba(255, 255, 255, 0.04);
        }
    }
    .listSection{
        flex: 1;
        padding-left: 16px;
        padding-right: 5px;
         max-height: calc(100% - 165px);
        // max-height: 83.13%;
        .savedSearchListContainer {
            height: 100%;
            overflow: auto;
            padding-right: 5px;
            &::-webkit-scrollbar {
                width: 5px;
                height: 5cm;
            }
        }
    }
    .listActionContainer{
        display: flex;
        margin: 16px;
        gap: 16px;
        overflow: hidden;
        justify-content: space-between;
        width: 100%;
        
    }
    .createNew{
    //    width: 100%;
    //    margin: 16px;
    //    overflow: hidden;
       position: relative;
    //    display: flex;
       
       
    }
    .createNewBtn {
        height: 36px;
        background-color: rgba(255, 255, 255, 0.04);
        border-radius: 9999px;
        border: none;
        cursor: pointer;
        padding: 0 4px 0 16px;
        display: flex;
        align-items: center;
        transition: background-color 0.2s;
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.08);
        cursor: not-allowed;

        .createButtonIcon {
            margin-left: 8px;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    .createNewBtnHover{
        height: 36px;
        border-radius: 9999px;
        border: none;
        cursor: pointer;
        padding: 0 4px 0 16px;
        display: flex;
        align-items: center;
        transition: background-color 0.2s;
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: left;
        background-color: #fff;
        color: #393e47;
        opacity: 0.5;
        &:hover {
            opacity: 1;
        }

        .createButtonIcon {
            margin-left: 8px;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    .titleSection{
        width: 100%;
        padding: 0px 16px;
    }
}
.dropDownBG.dropDownBG {
width: 110px;
z-index: 999;
padding: 4px;
border-radius: 8px;
-webkit-backdrop-filter: blur(20px);
backdrop-filter: blur(20px);
background-color: #9c9da5;
margin-top: 10px;

    ul {
        padding: 0px;

        li {
        font-family: Inter;
        font-size: 14px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: center;
        color: #191a20;
        margin-bottom: 2px;
        &[aria-selected="true"] {
            border-radius: 6px;
            background-color: #e0e0e0;
        }
        &:hover {
            border-radius: 6px;
            background-color: #e0e0e0;
        }
        }
    }
}
.searchContainer {
    width: 100%;
    margin-bottom: 10px; 
    background-color: #222329;
    border-radius: 8px;
    &:last-child{
        margin-bottom: 0px;
    }

    .searchItemContainerMain {
        &:last-child {
            .searchItemContainer {
                margin-bottom: 0px;
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
            }
        }
    }
    
    .searchLabel {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
    }
    .searchLabel {
        background-color: #303136;
        height: 40px;
        display: flex;
        align-items: center;
        padding-left: 12px;
        font-family: Syncopate;
        font-size: 12px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: 0.84px;
        text-align: left;
        color: #fff;
        text-transform: uppercase;
    }
    .searchItemContainer {
        background-color: #222329;
        padding: 8px 12px;
        overflow: hidden;
        transition: background-color 0.2s;
        margin-bottom: 2px;
        cursor: pointer;
        &:hover{
              background-color: #c3c4ca;
          .searchTitle,.searchDetails {
                 color: #0f0f14;

          }
           .iconContainer.iconContainer {
                  svg{
                    path{
                        fill: #0f0f14;
                    }
                  }

                   .shareIcon {
                    &:hover{
                        svg{
                            path{
                                fill: #0f0f14;
                            }
                        }
                    }
                }
                .deleteIcon {
                    &:hover{
                        svg{
                            path{
                                fill: #0f0f14;
                            }
                        }
                    }
                }
                
            }
            .orderNumber{
                .orderNumberText{
                  color: #0f0f14;
                }
              }
        }
        .searchTitle {
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: 0.98px;
            text-align: left;
            color: #fff;
            display: flex;
            position: relative;
            padding-bottom: 17px;
            position: relative;
            .searchTitleText {
                display: flex;
                align-items: center;
                gap: 4px;
                width: 60%;
                .searchTitleTextSpan {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: calc(100% - 30px);
                }
            }
            .itemCount {
                margin-left: auto;
                display: flex;
                align-items: center;
                column-gap: 4px;
            }
            .iconContainer {
                position: absolute;
                right: 0;
                top: 28px;
                display: flex;
                gap: 8px;
                .shareIcon {
                    cursor: pointer;
                    &.pricingExpired{
                        cursor: not-allowed;
                        opacity: 0.5;
                    }
                    &:hover{
                        svg{
                            path{
                                fill: #fff;
                            }
                        }
                    }
                }
                .deleteIcon {
                    cursor: pointer;
                    &:hover{
                        svg{
                            path{
                                fill: #fff;
                            }
                        }
                    }
                }
            }
        }
        .searchDetails {
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: 0.84px;
            text-align: left;
            color: #9b9eac;
            padding-bottom: 17px;
            display: flex;
            flex-direction: column;
            padding-right: 55px;
        }
    }
    .selectedSearchItem {
        background-color: #434449;
        &:hover{
            background-color: #434449;
        .searchTitle,.searchDetails {
               color: #fff;

        }
         .iconContainer.iconContainer {
                svg{
                  path{
                      fill: #fff;
                  }
                }

                 .shareIcon {
                  &:hover{
                      svg{
                          path{
                              fill: #fff;
                          }
                      }
                  }
              }
              .deleteIcon {
                  &:hover{
                      svg{
                          path{
                              fill: #fff;
                          }
                      }
                  }
              }
              
          }
          .orderNumber{
            .orderNumberText{
              color: #fff;
            }
          }
      }

        .iconContainer {
            position: absolute;
            right: 0;
            top: 28px;
            display: flex;
            gap: 8px;

            .shareIcon {
                cursor: pointer;

                &.pricingExpired{
                    cursor: not-allowed;
                    opacity: 0.5;
                }

                svg {
                    path {
                        fill: #fff;
                    }
                }
            }

            .deleteIcon {
                cursor: pointer;

                svg {
                    path {
                        fill: #fff;
                    }
                }
            }
        }
        .searchDetails {
            color: #fff;
        }
        .orderNumber{
            .orderNumberText{
              color: #fff;
            }
          }
    }
    
}
    
.noDataContainer {
    background-color: #222329;
    padding: 16px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    
    .noDataMessage {
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.98px;
        text-align: center;
        color: #9b9eac;
    }
}

.topSectionBtnLeft{
    display: flex;
    align-items: center;
    width: 100%;
}

.deleteContainer {
  height: 36px;
  min-width: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 12px;
  border-radius: 500px;
  background-color: rgba(255, 255, 255, 0.04);
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 1.4px;
  text-align: left;
  color: #fff;
  white-space: nowrap;
  transition: all 0.1s;
  cursor: pointer;
  svg{
    margin-right: 4px;
  }
  span{
    display: flex;
  }

  &:hover{
    color: #ff4848;
    svg{
        path{
            fill:#ff4848;
        }
    }
  }
}

.undoContainer{
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 12px;
  border-radius: 500px;
  background-color: rgba(255, 255, 255, 0.04);
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 1.4px;
  text-align: left;
  color: #fff;
  white-space: nowrap;
  transition: all 0.1s;
  cursor: pointer;
}

.editIcon{
    cursor: pointer;
}

.editTitleRow input {
    width: calc(80% - 30px);
    height: 21px;
    background-color: #434449;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    letter-spacing: 1.4px;
    text-align: left;
    color: #fff;
    border: 0px;
    border-radius: 4px;
    padding: 0px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;

    &:focus {
        outline: none;
    }
}

@keyframes slideInFromLeft5 {
    0% {
        left: -100%;
    }
    66.67% {
        left: -100%;
    }
    100% {
        left: 0;
    }
}

@keyframes slideInFromLeft1 {
    0% {
        left: -100%;
    }
    100% {
        left: 0;
    }
}
@keyframes slideInFromLeft2 {
    0% {
        left: -100%;
    }
    50% {
        left: -100%;
    }
    66.67% {
        left: 0%;
    }
    100% {
        left: 0;
    }
}
@keyframes slideInFromLeft3 {
    0% {
        left: -100%;
    }
    66.67% {
        left: -100%;
    }
    83.33% {
        left: 0%;
    }
    100% {
        left: 0;
    }
}
@keyframes slideInFromLeft4 {
    0% {
        left: -100%;
    }
    83.33% {
        left: -100%;
    }
    100% {
        left: 0;
    }
}
.initialPositionForAnimation{
    position: absolute;
    z-index: 1;
    top: 0;
    left: -100%;
}
  
.slideInAnimation5 {
    animation: slideInFromLeft2 3s ease-in-out forwards;
}

.slideInAnimation1 {
    animation: slideInFromLeft1 1s ease-in-out forwards;
}
.slideInAnimation2 {
    animation: slideInFromLeft2 2s ease-in-out forwards;
}
.slideInAnimation3 {
    animation: slideInFromLeft2 2s ease-in-out forwards;
}
.slideInAnimation4 {
    animation: slideInFromLeft2 2s ease-in-out forwards;
}

.positionRelative{
    position: relative;
    min-height: 16px;
}

// Export dropdown styles
.exportContainer.exportContainer {
    position: relative;
    border-radius: 500px;
  
    .exportButton {
      height: 34px;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0px 0px 0px 12px;
      background-color: rgba(255, 255, 255, 0.04);
      color: #9b9eac;
      border: none;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      cursor: pointer;
      transition: all 0.2s ease;
      &[disabled]{
         opacity: 0.5;
          background-color: rgba(255, 255, 255, 0.04);
          color: #9b9eac;
         cursor: not-allowed;
          &:hover {
         background-color: rgba(255, 255, 255, 0.04);
  
      }
      }
  
      .exportArrow {
        height: 100%;
        min-width: 30px;
        transition: transform 0.2s ease;
        border-radius: 0% 50% 50% 0%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-left: solid 1px rgba(255, 255, 255, 0.1);
  
        svg {
          width: 20px;
          height: 20px;
        }
      }
    }
  
    .exportDropdown.exportDropdown {
      position: absolute;
      top: 100%;
      left: 0;
      margin-top: 4px;
      border-radius: 6px;
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      background-color: #9c9da5;
      z-index: 1000;
      overflow: hidden;
      padding: 4px;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 4px;
      width: 123px;
  
      .exportOption {
        width: 100%;
        padding: 6px 7px 6px 5px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        font-family: Inter;
        font-size: 14px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        color: #191a20;
        text-align: left;
        margin-bottom: 2px;
  
        &:hover {
          border-radius: 6px;
          background-color: #e0e0e0;
          font-weight: bold;
        }
      }
    }
  }

  .exportDropdownMenu.exportDropdownMenu {
    width: 136px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;
      padding: 4px;
      border-radius: 8px;
      -webkit-backdrop-filter: blur(7px);
      backdrop-filter: blur(7px);
      background-color: rgba(255, 255, 255, 0.25);
      box-shadow: none;

      .exportOption {
          width: 100%;
          align-self: stretch;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          padding: 6px 12px;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.3;
          letter-spacing: normal;
          text-align: center;
          color: rgba(255, 255, 255, 0.7);

          &:hover {
              border-radius: 6px;
              background-color: rgba(255, 255, 255, 0.2);
              color: #fff;
              font-weight: bold;
          }
      }
  }

  .selectedProductHeaderButton,.deleteIcon{
    transition: all 0.2s ease;
    &:hover{
        svg{
            path{
                fill: #fff;
            }
        }
    }
  }
  .backToReviewBtn {
    padding: 8px 10px;
    border-radius: 500px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #9b9eac;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: right;
    gap: 10px;
    svg{
        width: 12px;
        height: 11px;
    }
    &[disabled] {
      opacity: 0.5;
      cursor: not-allowed;
      &:hover {
        color: #9b9eac;
        background-color: rgba(255, 255, 255, 0.04);
        svg{
            path{
                stroke: #9b9eac;
            }
        }
      }
    }
    &:hover {
      background-color: rgba(255, 255, 255, 0.08);
      color: #fff;
      svg{
        path{
            stroke: #fff;
        }
    }
    }
  }
  .orderNumber{
    position: relative;
    .orderNumberText{
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.98px;
      color: #fff;
      position: absolute;
      top: -17px;
    }
  }
