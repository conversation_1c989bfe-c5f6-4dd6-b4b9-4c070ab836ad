import React, { useState } from 'react'
import styles from '../../../buyer/CreatePo/CreatePo.module.scss';
import clsx from 'clsx';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import { disputeCounterStatus, LargeProductsNameList } from 'src/renderer2/common';
import { priceUnits, useBuyerSettingStore, useGlobalStore, userRole } from '@bryzos/giss-ui-library';
import { ReactComponent as DropDownArrowIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import { MenuItem, Select, SelectChangeEvent } from '@mui/material';
import dayjs from 'dayjs';
import Calendar from 'src/renderer2/component/Calendar/Calendar';
import { ClickAwayListener } from '@mui/material';

const DisputeTile = ({
    index,
    actualIndex,
    register,
    watch,
    setValue
}: {
    index: number;
    actualIndex: number;
    register: any;
    watch: any;
    setValue: any;
}) => {
    const [isAcceptDropdownOpen, setIsAcceptDropdownOpen] = useState(false);
    const [isCancelDropdownOpen, setIsCancelDropdownOpen] = useState(false);
    const [isCounter, setIsCounter] = useState(false);
    const [counterQty, setCounterQty] = useState("");
    const [counterQtyUnit, setCounterQtyUnit] = useState("");
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const userData = useGlobalStore((state: any) => state.userData);
    const deliveryAllowedDates = useBuyerSettingStore(state => state.deliveryAllowedDates);

    const lastCounterData = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 ? watch(`cart_items.${index}.line_dispute_counter`)[watch(`cart_items.${index}.line_dispute_counter`)?.length - 1] : {};
    const isCounterResolved = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && lastCounterData?.counter_status === disputeCounterStatus.resolved;
    const isLastCounterFromBuyerAndNotOriginal = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && lastCounterData?.created_by === userRole.buyerUser && lastCounterData?.counter_status !== disputeCounterStatus.original;
    const isRejectReasonPresent = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && lastCounterData?.reject_reason;
    const initialLineDisputeCounter = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 ? watch(`cart_items.${index}.line_dispute_counter`)[0] : {};

    const handleCounterClick = () => {
        const lastCounter = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 ? watch(`cart_items.${index}.line_dispute_counter`)[watch(`cart_items.${index}.line_dispute_counter`)?.length - 1] : {};
        setCounterQty(lastCounter?.qty || "");
        setCounterQtyUnit(lastCounter?.qty_unit.toLowerCase() || "");
        setIsCounter(!isCounter);
    }

    const handleOpenCalendar = () => {
        setIsCalendarOpen(true);
    };

    const handleDateSelect = (date: Date) => {
        // Update the delivery date in the dispute counter
        const formattedDate = dayjs(date).format('YYYY-MM-DD');
        setValue(`cart_items.${index}.line_dispute_counter.${watch(`cart_items.${index}.line_dispute_counter`)?.length - 1}.delivery_date`, formattedDate);
        setIsCalendarOpen(false);
    };

    const handleClickAway = () => {
        setIsCalendarOpen(false);
    };

    return (
        <tr className={clsx(styles.marginBottom)} id={`disputeTile-${actualIndex}`} >
            <td>
                <div className={styles.prodId}>
                    {/* <span className={styles.numberContainer}>{index + 1}</span> */}
                    <div className={styles.domesticMaterialCheckbox} >
                        <label className={styles.lineNumberContainer} data-hover-video-id={"domestic-only-po"}>
                            <input
                                type="checkbox"
                                {...register(`cart_items.${index}.domesticMaterialOnly`)}
                                checked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                                disabled={true}
                                // onChange={(e) => {
                                //     if (!watch('isEdit') || !showDomesticMaterialOnly || !(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) || watch(`cart_items.${index}.line_status`) === 'SKIPPED') {
                                //         return; // Prevent changes when visually disabled
                                //     }
                                //     register(`cart_items.${index}.domesticMaterialOnly`).onChange(e);
                                //     setIsCreatePoDirty(true);
                                //     saveUserActivity();
                                // }}
                                className={styles.hiddenCheckbox}
                                // We keep it enabled for keyboard navigation but mark it as aria-disabled
                                aria-disabled={true}
                            />
                            <span
                                className={clsx(
                                    styles.customNumberToggle,
                                    styles.disabled
                                    // watch(`cart_items.${index}.domesticMaterialOnly`) ? styles.active : "",
                                    // (!watch('isEdit') || !showDomesticMaterialOnly || (!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.line_status`) === 'SKIPPED') ? styles.disabled : "",
                                    // watch(`cart_items.${index}.line_status`) === 'SKIPPED' ? styles.domesticSkipDisabled : ""
                                )}
                                role="checkbox"
                                id={"domesticMaterialCheckbox-" + actualIndex}
                                aria-checked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                                aria-disabled={true}
                            // ref={lineNumberRef}
                            // tabIndex={(!watch('isEdit') || !showDomesticMaterialOnly || (!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.line_status`) === 'SKIPPED') ? -1 : 0}
                            >
                                {actualIndex + 1}
                            </span>
                            {/* <span className={clsx(styles.usaOnlyText, watch(`cart_items.${index}.domesticMaterialOnly`) ? "" : styles.visibilityHidden)}>USA<br />ONLY</span> */}
                        </label>
                    </div>
                </div>
            </td>
            <td>
                <div className={clsx(styles.poDescriptionDiv, styles.disabled)} >
                    <p className={styles.descriptionModeDisabled}>
                        <textarea
                            // data-hover-video-id={orderInfoIsFilled ? "entering-a-line-in-po" : ""}
                            // type="text"
                            name={register(`cart_items.${index}.descriptionObj`).name}
                            id={`productDescription-${actualIndex}`}
                            value={watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.toUpperCase() || ''}
                            placeholder={watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.toUpperCase() || ''}
                            className={clsx(styles.poDescription, (watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.length > 0) && styles.poDescriptionFirstLine,
                                (LargeProductsNameList?.find((largeProductsName) => watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.split("\n")[0]?.includes(largeProductsName))) && styles.poDescriptionFirstLine1
                            )}
                            // onClick={() => {
                            //     setIsDescriptionModeEnabled(true);
                            // }}
                            onFocus={() => {
                                // setIsHovering(true);
                                // setIsDescriptionModeEnabled(true)
                                // openAddLineTab()
                                // setUndoStackObject({ name: `descriptionObj`, value: watch(`cart_items.${index}.descriptionObj`), id: `cart_items.${index}.descriptionObj` });
                            }}
                            onBlur={(e) => {
                                // setIsHovering(false);
                            }}
                            onKeyDown={(e) => {
                                if (e.key === 'Tab' && e.shiftKey) {
                                    // const isCheckboxDisabled = !showDomesticMaterialOnly ||
                                    //     !(watch(`cart_items.${index}.descriptionObj`) &&
                                    //         Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0);

                                    // Skip focusing the domestic material checkbox if it's disabled
                                    // if (isCheckboxDisabled) {
                                    //     return; // Let default tab behavior work
                                    // }

                                    // e.preventDefault();
                                    // if (lineNumberRef && lineNumberRef.current) {
                                    //     setIsHovering(false);
                                    //     setIsDomesticFocused(true);
                                    //     lineNumberRef.current.focus();
                                    // }

                                    // setOpenDeliveryToDialog(false);
                                    // setTimeout(() => {
                                    //   const descriptionInput = document.querySelector('input[name="shipping_details.zip"]');
                                    //   if (descriptionInput instanceof HTMLElement) {
                                    //     descriptionInput.focus();
                                    //   }
                                    // }, 100);
                                }
                            }}
                            disabled={true}
                            readOnly
                        />
                    </p>
                    {watch(`cart_items.${index}.descriptionObj`)?.UI_Description &&
                        <div className={styles.partNumberFiled}
                            data-hover-video-id="part-number-po">
                            <InputWrapper>
                                <CustomTextField
                                    // dataHoverVideoId="part-number-po"
                                    type='text'
                                    register={register(`cart_items.${index}.product_tag`)}
                                    disabled={true}
                                    placeholder="Add YOUR PART #"
                                    // className={clsx({ [styles.errorInput]: errors?.cart_items?.[index]?.product_tag?.message })}
                                    value={watch(`cart_items.${index}.product_tag`) ?? ""}
                                // onmouseenter={() => {
                                //     setIsHovering(true);
                                // }}
                                // onFocus={() => {
                                //     setIsHovering(true);
                                //     setIsDescriptionModeEnabled(false);
                                //     setUndoStackObject({ name: `product_tag`, value: watch(`cart_items.${index}.product_tag`), id: `cart_items.${index}.product_tag` });
                                // }}
                                // onBlur={(e) => {
                                //     register(`cart_items.${index}.product_tag`).onBlur(e);
                                //     saveUserActivity();
                                //     setIsHovering(false);
                                //     if (handleStoreUndoStack && isDataChanged) {
                                //         handleStoreUndoStack({ ...undoStackObject, currentValue: e.target.value, from: "line", actualIndex: actualIndex, index: index, uniqueId: actualIndex });
                                //         setIsDataChanged(false);
                                //     }
                                //     setUndoStackObject({});
                                //     setIsQtyInEditMode(true);
                                // }}
                                // onChange={(e) => {
                                //     setIsDataChanged(true);
                                // }}
                                // onKeyUp={(e) => {
                                //     setValue(
                                //         `cart_items.${index}.product_tag`,
                                //         e.target.value
                                //     );
                                // }}
                                />
                            </InputWrapper>
                        </div>
                    }

                </div>
            </td>
            <td colSpan={3} className={styles.disputetblCol}>
                <div className={styles.disputeContainer}>
                    <div className={styles.disputeCounterContainer1}>
                        {watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 &&
                            watch(`cart_items.${index}.line_dispute_counter`)?.map((dispute: any) => {
                                const isMe = userData?.data?.type === dispute?.created_by;
                                const isBuyer = dispute?.created_by === userRole.buyerUser;
                                return (
                                    <div className={styles.disputeCounterData} key={dispute.counter_number}>
                                        <span className={styles.disputeCounterDataFirstCol}>
                                           <span className={styles.disputeCounterStatus}>
                                            <span className={styles.disputeCounterStatusLbl}>{dispute?.counter_status === disputeCounterStatus.original ? "Original " : isMe ? "Me " : isBuyer ? "Buyer" : "Supplier"}</span>
                                             {dispute?.counter_status !== disputeCounterStatus.original && `(${dispute.counter_number})`}
                                          </span>
                                           <span className={styles.disputeCounterQtyUnit}><span>{dispute.qty}</span> <span>{dispute.qty_unit}</span></span>
                                        </span>
                                        <span className={styles.disputeCounterPriceUnit}>{dispute.buyer_price_per_unit} {dispute.price_unit}</span>
                                        <span className={styles.disputeCounterExtended}>{dispute.buyer_line_total}</span>
                                    </div>
                                )
                            })
                        }
                    </div>
                    {(initialLineDisputeCounter?.delivery_date && lastCounterData?.delivery_date && !isCounter) ?
                    (
                        <div>
                            <div>
                                <span>Deliver New Line by:</span>
                                <span>{dayjs(initialLineDisputeCounter?.delivery_date).format('ddd, MMMM D, YYYY')}</span>
                            </div>
                            <div>
                                <span>
                                    {userData?.data?.type === lastCounterData?.created_by ? "Me " : lastCounterData?.created_by === userRole.buyerUser ? "Buyer" : "Supplier"}
                                    {lastCounterData?.counter_status !== disputeCounterStatus.original && `(${lastCounterData?.counter_number})`}
                                </span>
                                <span>{dayjs(lastCounterData?.delivery_date).format('ddd, MMMM D, YYYY')}</span>
                            </div>
                        </div>
                    ) 
                    : (watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && isLastCounterFromBuyerAndNotOriginal) &&
                    (
                        <div key={watch(`cart_items.${index}.line_dispute_counter`)?.[watch(`cart_items.${index}.line_dispute_counter`)?.length - 1]?.counter_number}>
                            <span>
                                {userData?.data?.type === lastCounterData?.created_by ? "Me " : lastCounterData?.created_by === userRole.buyerUser ? "Buyer" : "Supplier"}
                                {lastCounterData?.counter_status !== disputeCounterStatus.original && `(${lastCounterData?.counter_number})`}
                            </span>
                            <span>{lastCounterData?.qty} {lastCounterData?.qty_unit}</span>
                            <span>{lastCounterData?.buyer_price_per_unit} {lastCounterData?.price_unit}</span>
                            <span>{lastCounterData?.buyer_line_total}</span>
                        </div>
                    )}
                    <div className={styles.disputeCounterBtnSection}>
                        {isCounter ? (
                            <div className={styles.disputeCounterInputGrid}>
                                <div className={styles.disputeInputGridMain}>
                                    <div className={clsx(styles.disputeInputGrid,styles.disputeBox)}>
                                        <InputWrapper>
                                            <CustomTextField
                                                type='number'
                                                disabled={false}
                                                placeholder="Enter Counter Qty"
                                                value={counterQty || ""}
                                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                    setCounterQty(e.target.value);
                                                }}
                                            />
                                        </InputWrapper>
                                        <div className={styles.selectQtyUnitDispute}>
                                        <Select
                                            value={counterQtyUnit}
                                            onChange={(e: SelectChangeEvent<string>) => {
                                                setCounterQtyUnit(e.target.value);
                                            }}
                                           className={clsx(styles.uomDrodown,'qtyUnitDropdown')}
                                            MenuProps={{
                                                classes: {
                                                    paper: styles.selectUomPaper,
                                                },
                                            }}
                                        >
                                            {watch(`cart_items.${index}.qty_um`)?.map((x: string) => {
                                                console.log(counterQtyUnit);
                                                return (
                                                    <MenuItem key={x} value={x}>{x.toUpperCase()}</MenuItem>
                                                )
                                            })}
                                        </Select>
                                        </div>
                                    </div>
                                    <div className={clsx(styles.deliveryDateContainer,styles.disputeBox)}>
                                        <span>Deliver New Line by:</span>
                                        <ClickAwayListener onClickAway={handleClickAway}>
                                            <div className={styles.deliveryDateContainerData}>
                                                <span 
                                                    className={styles.clickableDeliveryDate}
                                                    onClick={handleOpenCalendar}
                                                    style={{ cursor: 'pointer' }}
                                                >
                                                    {dayjs(lastCounterData?.delivery_date).format('ddd, MMMM D, YYYY')}
                                                </span>
                                                {isCalendarOpen && (
                                                    <div className={styles.calendarWrapper}>
                                                        <Calendar
                                                            value={lastCounterData?.delivery_date || ''}
                                                            setValue={(field: string, value: string) => {
                                                                if (field === 'delivery_date') {
                                                                    setValue(`cart_items.${index}.line_dispute_counter.${watch(`cart_items.${index}.line_dispute_counter`)?.length - 1}.delivery_date`, value);
                                                                }
                                                            }}
                                                            isCalendarOpen={isCalendarOpen}
                                                            setIsCalendarOpen={setIsCalendarOpen}
                                                            disableDeliveryDate={false}
                                                            handleOpenCalendar={handleOpenCalendar}
                                                            saveUserActivity={() => {}}
                                                            saveBomHeaderDetails={() => {}}
                                                            onDateSelect={handleDateSelect}
                                                            allowedDates={deliveryAllowedDates}
                                                        />
                                                    </div>
                                                )}
                                            </div>
                                        </ClickAwayListener>
                                    </div>
                                </div>
                                <div className={styles.disputeCounterInputBtnGrid}>
                                    <button className={styles.btnAcceptCounter} onClick={() => { setIsCounter(false) }}>Cancel Counter</button>
                                    <button className={styles.btnAcceptCounter} onClick={() => { }} disabled={false}>Submit</button>
                                </div>
                            </div>
                        ) : isCounterResolved ? (
                                <div className={styles.actionRequiredContainer}>
                                    <span>No action required.</span>
                                    <span>The supplier has accepted your request.</span>
                                </div>
                        ) : isRejectReasonPresent ? (
                            <div>
                                <div>
                                    <span>{lastCounterData?.reject_reason}</span>
                                </div>
                            </div>
                        ) : (
                            <div className={styles.disputeCounterBtnGrid}>
                                {/* <div className={styles.exportContainer}> */}
                                    <button className={styles.btnAcceptCounter} onClick={() => {}}
                                    // disabled={selectedProductsData.length === 0}
                                    >
                                        Accept
                                        {/* <span className={styles.exportArrow}><DropDownArrowIcon /></span> */}
                                    </button>
                                    {/* {isAcceptDropdownOpen && (
                                        <div className={styles.exportDropdown}>
                                            <button
                                                className={styles.exportOption}
                                                onClick={() => { }}
                                                disabled={false}
                                            >
                                                Accept Counter As Is
                                            </button>
                                            <button
                                                className={styles.exportOption}
                                                onClick={() => { }}
                                                disabled={false}
                                            >
                                                Accept Counter & Resource Remaining Pcs
                                            </button>
                                        </div>
                                    )} */}
                                {/* </div> */}
                                <button className={styles.btnAcceptCounter} onClick={handleCounterClick}>
                                        Counter
                                </button>
                                <div className={styles.exportContainer}>
                                    <button
                                        className={clsx(styles.selectedProductHeaderButton, styles.exportButton)}
                                        onClick={() => setIsCancelDropdownOpen(!isCancelDropdownOpen)}
                                    // disabled={selectedProductsData.length === 0}
                                    >
                                        <span>Cancel</span>
                                        <span className={styles.exportArrow}><DropDownArrowIcon /></span>
                                    </button>
                                    {isCancelDropdownOpen && (
                                        <div className={styles.exportDropdown}>
                                            <button
                                                className={styles.exportOption}
                                                onClick={() => { }}
                                                disabled={false}
                                            >
                                                Cancel Line
                                            </button>
                                            <button
                                                className={styles.exportOption}
                                                onClick={() => { }}
                                                disabled={false}
                                            >
                                                Revert to Original
                                            </button>
                                        </div>
                                    )}
                                </div>

                            </div>

                        )}
                    </div>
                </div>
            </td >
        </tr >
    )
}

export default DisputeTile