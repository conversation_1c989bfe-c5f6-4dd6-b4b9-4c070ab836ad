.orderContent {
    border-radius: 0px 0px 10px 10px;
    -webkit-backdrop-filter: blur(60px);
    backdrop-filter: blur(60px);
    background-color: rgba(0, 0, 0, 0.75);
    margin: 0px auto;
    max-width: 800px;
    width: 100%;
    text-align: center;
    position: relative;

    .orderSearch {
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        width: 100%;
        height: 79px;
        align-items: center;
        padding-right: 16px;

        input {
            border: 0;
            padding: 0px 16px;
            font-family: Noto Sans;
            font-size: 20px;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            background-color: transparent;

            &::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            &:focus-visible {
                outline: transparent;
            }
        }

        .btnOfOrderSearch {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 5px;

            button {
                font-family: Noto Sans;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.4;
                text-align: left;
                color: rgba(255, 255, 255);
                width: 141px;
                height: 21px;
                padding: 2px 0;
                border-radius: 2px;
                border: solid 0.5px rgba(255, 255, 255);
                background-color: transparent;
                text-align: center;
                opacity: 0.5;
                &:hover{
                    background-color: #70ff00;
                    opacity: unset;
                    color: #000;
                    border: 1px solid #000;
                    font-weight: normal;
                }
            }
            .activeBtn{
                background-color: #70ff00;
                opacity: unset;
                color: #000;
                border: 1px solid #000;
                font-weight: normal;
            }
        }
    }

    .orderHeading {
        height: 68px;
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 16px 12px;
        gap: 2px;

        .bigHeading {
            font-family: Noto Sans;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.4;
            text-align: left;
            color: #fff;

            span {
                font-family: Noto Sans;
                font-size: 14px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: rgba(255, 255, 255);
                padding: 1px 6px;
                border-radius: 2.8px;
                border: solid 0.5px rgba(255, 255, 255);
                background-color: rgba(0, 0, 0, 0.5);
                opacity: 0.5;
            }
        }

        .smallHeading {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: 300;
            line-height: 1.4;
            text-align: left;
            color: #fff;
        }
    }

    .listOfOrder {
        padding-right: 4px;

        ul {
            list-style: none;
            padding: 0px 4px 0px 12px;
            overflow-y: auto;
            height: 400px;

            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }
            li {
                height: 105px;
                padding: 16px 12px;
                width: 100%;
                display: flex;
                margin-top: 4px;
                border-radius: 4px;
                border: 1px solid transparent;
                cursor: pointer;
                &:hover{
                    background-color: rgba(255, 255, 255, 0.75);
                    div {
                        color: #000;
                        &:nth-child(3) {
    
                            .btnOfAHText {
    
                                span {
                                    color: #000;
                                    padding: 1px 6px;
                                    border-radius: 2.8px;
                                    border: solid 0.5px #000;
                                    background-color: transparent;
                                    margin-left: 6px;
                                    opacity: unset;
                                }
                            }
                        }
                    }
                }
                .bedgeLogin {
                    width: 12px;
                    height: 12px;
                    background-color: #ff0000;
                    border-radius: 50%;
                    display: inline-flex;
                    margin-left: 4px;
                    position: relative;
                    z-index: 1;
                    margin-top: 10px;
                    cursor: pointer;
                    top: 1px;
                }

                .disputesBlue {
                    width: 12px;
                    height: 12px;
                    background-color: #397aff;
                    border-radius: 50%;
                    display: inline-flex;
                    margin-left: 4px;
                    position: relative;
                    z-index: 1;
                  margin-top: 10px;
                    cursor: pointer;
                    top: 1px;
                }
                div {
                    color: #fff;
                    &:nth-child(1) {
                        width: 24px;
                    }

                    &:nth-child(2) {
                        width: 100%;
                        padding-left: 5px;
                        text-align: left;

                        .firstLine {
                            font-family: Noto Sans;
                            font-size: 18px;
                            font-weight: 500;
                            line-height: 1.4;
                            text-align: left;
                        }

                        .secondLine {
                            font-family: Noto Sans;
                            font-size: 16px;
                            font-weight: 300;
                            line-height: 1.4;
                            letter-spacing: normal;
                            text-align: left;

                            span {
                                font-weight: 600;
                                letter-spacing: 2.4px;
                            }
                        }
                    }

                    &:nth-child(3) {
                        display: flex;
                        width: 100%;
                        justify-content: right;

                        .firstTdText {
                            font-family: Noto Sans;
                            font-size: 14px;
                            font-weight: 300;
                            line-height: 1.4;
                            text-align: left;
                        }

                        .dollerText {
                            font-family: Noto Sans;
                            font-size: 14px;
                            line-height: 1.4;
                            text-align: right;
                            padding-left: 12px;
                        }

                        .btnOfAHText {
                            padding-top: 6px;
                            font-family: Noto Sans;
                            font-size: 14px;
                            font-weight: 300;
                            font-stretch: normal;
                            font-style: normal;
                            line-height: 1.4;
                            letter-spacing: normal;
                            text-align: right;

                            span {
                                font-size: 12px;      
                                font-weight: 300px;                        
                                color: rgba(255, 255, 255);
                                padding: 1px 6px;
                                border-radius: 2.8px;
                                border: solid 0.5px rgba(255, 255, 255);
                                background-color: rgba(0, 0, 0, 0.5);
                                opacity: 0.5;
                                margin-left: 6px;
                                &:hover{
                                    background-color: #70ff00;
                                    opacity: unset;
                                    color: #000;
                                    border: solid 1px #000;
                                    font-size: 12px;
                                    font-weight: 600;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .btnSection {
        display: grid;
        grid-template-columns: 20% 60% 20%;
        border-top: 1px solid #000;
        align-items: center;
        padding: 8px 0px;
        margin: 30px 16px 0px 16px;

        div {
            &:nth-child(1) {
                text-align: left;
            }
        }

        .TermsandConditions {
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: 300;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            cursor: pointer;
        }

        .backBtn {
            font-family: Noto Sans;
            font-size: 18px;
            font-weight: normal;
            line-height: 1.6;
            color: #fff;
            border: 0px;
            outline: none;
            background-color: transparent;
            text-align: left;
            cursor: pointer;
            padding-left: 10px;
            &:hover{
                color: #b3b3b3;
            }
        }
    }
}

.autocompleteDescPanel {
    border-radius: 4px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(255, 255, 255, 0.3);
  }
  
  .autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    -webkit-backdrop-filter: blur(60px);
    backdrop-filter: blur(60px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(255, 255, 255, 0.3);
  }
  
  .listAutoComletePanel.listAutoComletePanel {
    width: 100%;
    max-height: 316px;
    padding: 6px 4px 6px 10px;
  
    &::-webkit-scrollbar {
      width: 6px;
    }

    span {
        font-family: Noto Sans;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        box-shadow: none;
        padding: 4px 8px;
        flex-direction: column;
        align-items: flex-start;
    
        &:hover {
          border-radius: 2px;
          background-color: #fff;
          color: #000;
        }
    
        &[aria-selected="true"] {
          background-color: #EBF2FF;
          color: #397aff;
        }
    }
}

.poDescription {
    padding: 16px;
    background-color:transparent;
    color: #fff;
    resize: none;
    font-family: Noto Sans;
    font-size: 20px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    outline: none;
    border:none;
    width: 100%;
    &::placeholder {
      color: #bbb;
    }   
  }

  