import VideoPlayerRightWindow from "../../pages/RightWindow/VideoPlayerRightWindow/VideoPlayerRightWindow"
import OrderSummary from "../OrderSummary/OrderSummary"
import { useRightWindowStore } from "../../pages/RightWindow/RightWindowStore";
import BomProcessingWindow from "../BomProcessingWindow/BomProcessingWindow";
import { routes, shareEmailTypes } from "src/renderer2/common";
import ShareEmailWindow from "../ShareEmailWindow/ShareEmailWindow";
import ChatWithVendor from "../ChatWithVendor/ChatWithVendor";
import { useEffect, useRef } from "react";
import { useChatWithVendorStore, useCreatePoStore } from "@bryzos/giss-ui-library";
import { useAvailableHeight } from "src/renderer2/pages/buyer/BomPdfExtractor/utils/useAvailableHeight";

const UploadReviewWindow = ({orderSummaryProps}:any) => {
  const {showVideo, showBomProcessing} = useRightWindowStore();
  const { shareEmailType } = useRightWindowStore();
  const {selectedQuote} = useCreatePoStore();
  const {setChannelName, setPoNumber, setCompanyName} = useChatWithVendorStore();
  useEffect(()=>{
    if(location.pathname === routes.orderManagementPage){
      setPoNumber(selectedQuote?.buyer_internal_po)
      setChannelName(selectedQuote?.buyer_po_number)
      setCompanyName(selectedQuote?.seller_company_name ? selectedQuote.seller_company_name : 'Seller Company Name')
    }
  },[selectedQuote])
  return (
    <>
      <OrderSummary {...orderSummaryProps} />
      {/* {(showVideo && location.pathname !== routes.savedBom) && <VideoPlayerRightWindow />} */}
      {showBomProcessing && <BomProcessingWindow hideScore={true} />}
      {shareEmailType === shareEmailTypes.shareQuote && <ShareEmailWindow />}
      {
        // (location.pathname === routes.orderManagementPage && selectedQuote && selectedQuote?.seller_id) ? 
        (false) ? 
        (<ChatWithVendor 
          close = {() => {}}
          userName = {selectedQuote?.seller_name ||'Seller Name'}
        />):(<></>)
      }

    </>
  )
}

export default UploadReviewWindow
