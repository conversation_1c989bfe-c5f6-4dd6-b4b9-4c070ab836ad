// chatUtils.js

/**
 * Formats a JS Date object into a readable string, e.g., "Jul 10, 2025 2:32pm"
 */
export function formatDate(date) {
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
                    "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const month = months[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();
    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const ampm = hours >= 12 ? "pm" : "am";
    hours = hours % 12 || 12;
    return `${month} ${day}, ${year} ${hours}:${minutes}${ampm}`;
}

/**
 * Cleans up HTML from a contentEditable, removing leading/trailing whitespace, <br>, and &nbsp;
 */
export function cleanContentEditableHtml(html) {
    html = html.replace(/^((\s|&nbsp;|<br>)+)/gi, "");
    html = html.replace(/((\s|&nbsp;|<br>)+)$/gi, "");
    return html;
}

/**
 * Converts URLs in plain text to anchor tags. Optionally add a className for styling.
 */
export function linkify(text, className = '') {
    const urlRegex = /(https?:\/\/[^\s<]+)|(www\.[^\s<]+)/gi;
    return text.replace(urlRegex, (url) => {
        let link = url;
        if (!link.startsWith('http')) link = 'http://' + link;
        return `<a href="${link}"${className ? ` class="${className}"` : ''} target="_blank" rel="noopener noreferrer">${url}</a>`;
    });
}
/**
 * Helper: extract links from text and split string into parts
 * * @param {object} text - Text received from chat.
 */

export const parseLinks = (text: string) => {
    const urlRegex = /(https?:\/\/[^\s<]+)|(www\.[^\s<]+)/gi;
    const elements: (string | { url: string })[] = [];
    let lastIndex = 0;
  
    let match: RegExpExecArray | null;
    while ((match = urlRegex.exec(text)) !== null) {
      // Push text before the link
      if (match.index > lastIndex) {
        elements.push(text.substring(lastIndex, match.index));
      }
      // Push the link
      elements.push({ url: match[0] });
      lastIndex = match.index + match[0].length;
    }
    // Push any remaining text
    if (lastIndex < text.length) {
      elements.push(text.substring(lastIndex));
    }
    return elements;
  };

/**
 * Checks if a scrollable container is scrolled near the bottom. Default threshold: 30px
 */
export function isScrollAtBottom(container, threshold = 30) {
    if (!container) return false;
    return (container.scrollHeight - container.scrollTop) - container.clientHeight < threshold;
}

/**
 * Scrolls a container to the bottom (smoothly)
 */
export function scrollToBottom(container) {
    if (container) {
        container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
        });
    }
}

/**
 * Normalizes a raw backend message object into a frontend message structure.
 * @param {object} item - Message object from backend (with .message as JSON string)
 * @param {string|number} myId - Current user's ID
 */
const ATTACHMENT = 'attachment';
export function normalizeMessage(item, myId) {
    const message = JSON.parse(item.message);
    
    // Check if it's a combined message
    if (message.isCombined) {
        return {
            id: message.id,
            text: message.text,
            attachments: message.attachments,
            fromName: message.fromName,
            fromId: message.fromId,
            messageId: message.messageId,
            formattedTimestamp: message.formattedTimestamp,
            timestamp: item.message_created_at,
            status: 'sent',
            isMyMessage: item.user_id === myId,
            isCombined: true
        };
    }
    
    // Handle regular attachment messages
    if (message.type === ATTACHMENT) {
        return {
            id: message.id,
            type: ATTACHMENT,
            name: message.name,
            fromName: message.fromName,
            fromId: message.fromId,
            messageId: message.messageId,
            extension: message.extension,
            timestamp: item.message_created_at,
            url: message.url,
            formattedTimestamp: message.formattedTimestamp,
            status: 'sent',
            isMyMessage: item.user_id === myId
        };
    }
    
    // Handle regular text messages
    if (message.text && typeof message.text === 'string') {
        message.text = message.text.replace(/<br\s*\/?>/gi, '\n');
    }
    message.timestamp = item.message_created_at;
    message.fromName = item.user_name;
    message.fromId = item.user_id;
    message.messageId = item.message_id;
    message.isMyMessage = item.user_id === myId;
    message.formattedTimestamp = formatDate(new Date(message.timestamp));
    return message;
}

export function shortenFileName(filename: string, maxLength: number = 8): string {
    const lastDot = filename.lastIndexOf(".");
    if (lastDot === -1) return filename; // no extension
  
    const name = filename.slice(0, lastDot);
    const ext = filename.slice(lastDot);
  
    if (name.length <= maxLength) return filename;
  
    return name.slice(0, maxLength) + ".." + ext;
  }