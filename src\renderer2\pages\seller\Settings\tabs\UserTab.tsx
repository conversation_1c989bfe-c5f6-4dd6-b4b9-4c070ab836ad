import React, { useState, useEffect, useRef } from 'react';
import styles from './TabContent.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  FieldErrors,
  FieldValues,
  useForm,
  UseFormSetError,
  UseFormSetValue,
} from 'react-hook-form';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import MultiStateSelector from 'src/renderer2/component/MultiStateSelector/MultiStateSelector';
import clsx from 'clsx';
import { Dialog } from '@mui/material';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import ChangePassword from 'src/renderer2/component/changePassword/changePassword';
import { buyerSettingConst, commomKeys, decryptData, emojiRemoverRegex, encryptData, formatPhoneNumberRemovingCountryCode, formatPhoneNumber, getChannelWindow, useBuyerSettingStore, useGlobalStore, usePostExpireOtherSession, usePostValidateEmail, useSellerSettingStore } from '@bryzos/giss-ui-library';
import {
  formatPhoneNumberWithHyphen,
  unformatPhoneNumber,
} from 'src/renderer2/helper';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import usePostVerifyZipCode from 'src/renderer2/hooks/usePostVerifyZipCode';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { Auth } from 'aws-amplify';
import { userSchema } from '../schemas/userSchema';
interface InputFocusState {
  userType: boolean;
  firstName: boolean;
  lastName: boolean;
  email: boolean;
  password: boolean;
  phoneNumber: boolean;
}

const UserTab: React.FC<{
  setSaveFunctions: any;
  routerContainerRef: React.RefObject<HTMLDivElement>;
}> = ({
  setSaveFunctions,
  routerContainerRef,
}) => {
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
    getFieldState,
  } = useForm({
    resolver: yupResolver(userSchema),
    mode: 'onSubmit',
  });

  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    userType: false,
    firstName: false,
    lastName: false,
    email: false,
    password: false,
    phoneNumber: false,
  });
  const [openChangePassPopup, setOpenChangePassPopup] = useState(false);
  const { deviceId, isImpersonatedUserLoggedIn, userData , setForceLogout, decryptionEntity }: any =
    useGlobalStore();
  const changePassPopupRef = useRef(null);
  const { mutateAsync: saveUserSettings } = useSaveUserSettings();
  const {showCommonDialog, resetDialogStore}: any = useDialogStore();
  const [showOtpDialog, setShowOtpDialog] = useState(false);;
  const [newEmail, setNewEmail] = useState('');
  const {mutateAsync: expireOtherSessions} = usePostExpireOtherSession();
  const {mutateAsync: validateEmail} = usePostValidateEmail();
  const {sellerSettings , setShowFadeLoader}: any = useSellerSettingStore();
  const isButtonDisabled = !isDirty || isSubmitting;


    useEffect(() => {
      if(sellerSettings) {
        setValue('firstName', sellerSettings?.first_name || '');
        setValue('lastName', sellerSettings?.last_name || '');
        setValue('email', sellerSettings?.email_id || '');
        setValue(
          'phoneNumber',
          sellerSettings?.phone
            ? formatPhoneNumber(
                formatPhoneNumberRemovingCountryCode(sellerSettings?.phone)
              )
            : ''
        );
      }
  }, [sellerSettings]);

  useEffect(() => {
    setSaveFunctions({
        onSave: () => handleSubmit(handleSaveUser)(),
        isDisabled: isButtonDisabled,
    });
  }, [isButtonDisabled, handleSubmit]);

  useEffect(() => {
    setTimeout(() => {
      const firstNameInput = document.getElementById('firstName');
      if (firstNameInput) {
        firstNameInput.focus();
      }
    }, 100)
  }, []);

  // Load user settings from localStorage on component mount
  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (
    inputName: keyof InputFocusState
  ) => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
  };

  const changePassPopup = () => {
    if (!isImpersonatedUserLoggedIn) setOpenChangePassPopup(true);
  };

  const handleEmailChange = (currentEmail: string) => {
    // Add any other logic you want to execute when email changes
    if(currentEmail !== sellerSettings?.email_id){
      //show a popup to confirm the email change
      // saveUserSettingsonBlur();
      showCommonDialog(null, 'Are you sure you want to change your email? You will need to login again with the new email if you proceed.', null, resetDialogStore, [{name: commomKeys.yes, action: ()=>resetUserEmail(true)}, {name: commomKeys.no, action: ()=>resetUserEmail(false)}])
    }
  };

  const resetUserEmail = async (proceed: boolean) => {
    resetDialogStore();
    try {
      if (proceed) {
      const validateEmailPayload = {
        data: {
          email_id: watch('email'),
        }
      }
      setShowFadeLoader(true);
      const validateEmailResponse = await validateEmail(validateEmailPayload);
      if (validateEmailResponse) {
        const expireSessionPayload = {
          data: {
            device_id: deviceId,
          }
        }
          await expireOtherSessions(expireSessionPayload);
          // Store the new email that needs to be verified
          const newEmail = watch('email');
          setNewEmail(newEmail);
          const user = await Auth.currentAuthenticatedUser();
          await Auth.updateUserAttributes(user, { email: newEmail });
          // Show OTP dialog for email verification
          setShowOtpDialog(true);
          // Make API call to send OTP to new email
          // TODO: Implement API call to send OTP
        }
        else {
          resetData();
          setShowFadeLoader(false);
        }
      }else{
        resetData();
      }
    } catch (error) {
      console.error('error', error);
      showCommonDialog(null, error?.message || 'Something went wrong', null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: () => {resetDialogStore(); resetData() }}]);
      setShowFadeLoader(false);
    }
  };

  const resetData = () => {
    reset({
      email: sellerSettings?.email_id,
      firstName: sellerSettings?.first_name,
      lastName: sellerSettings?.last_name,
      phoneNumber:  sellerSettings?.phone
      ? formatPhoneNumber(
          formatPhoneNumberRemovingCountryCode(sellerSettings?.phone)
        )
      : '',
    })
  }

  const {
    register: otpRegister,
    handleSubmit: otpHandleSubmit,
    formState: { errors: otpErrors },
    watch: otpWatch,
    reset: otpReset,
  } = useForm({
    defaultValues: {
      otp: '',
    },
  });

  const handleOtpSubmit = async (data: any) => {
    try{
      // TODO: Verify OTP with server
      // If successful, update the email
      // If failed, show error
      setShowFadeLoader(true);
      await Auth.verifyCurrentUserAttributeSubmit('email', data.otp); 
      const channelWindow = getChannelWindow();
      const cred = window.electron.sendSync({ channel: channelWindow.getLoginCredential });
      let password = null;
      if (cred) {
          const data = JSON.parse(await decryptData(cred, decryptionEntity.decryption_key, true));
          password = data.password;
      }
      document.cookie.split(";").forEach((c) => {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
      });
      await Auth.signIn(watch('email'), password);
      await handleSubmit((data) => handleSaveUser(data, true))();
      setShowOtpDialog(false);
      setForceLogout(true);
    }catch(error){
      showCommonDialog(null, error?.message || 'Something went wrong', null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: ()=>resetDialogStore()}]);
      console.error('error', error);
    }finally{
      otpReset();
      setShowFadeLoader(false);
    }
  };

  const handleOtpCancel = () => {
    setShowOtpDialog(false);
    otpReset();
    resetData();
    setShowFadeLoader(false);
  };

  const handleSaveUser = async (data: any, emailChanged: boolean = false) => {
    try {
      if (watch('email') !== sellerSettings?.email_id && !emailChanged) {
        handleEmailChange(watch('email'));
      } else {
        setShowFadeLoader(true);
        const payload = {
          first_name: data.firstName,
          last_name: data.lastName,
          email_id: data.email,
          phone: unformatPhoneNumber(data.phoneNumber),
        }
        await saveUserSettings({ route: 'user/seller/settings', data: payload })
        reset(data);
        setShowFadeLoader(false);
      }
    } catch (err) {
      console.error(err)
      setShowFadeLoader(false);
    }
  }


  return (
    <div className={clsx(styles.tabContent,styles.userTabContent)} ref={changePassPopupRef} data-hover-video-id="settings-user">
      <div className={styles.formContainer}>
        {/* USER TYPE */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.userType && styles.focusLbl)}
              htmlFor='userType'
            >
              USER TYPE
            </label>
          </span>
          <span className={styles.col1}>
            <div className={styles.defaultInput}>Seller</div>
          </span>
        </div>

        {/* YOUR FIRST & LAST NAME */}
        {/* YOUR FIRST & LAST NAME */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx((isInputFocused.firstName || isInputFocused.lastName) && styles.focusLbl)}
              htmlFor='firstName'
            >
              YOUR FIRST & LAST NAME
            </label>
          </span>
          <span className={clsx(styles.col1,styles.colGap)}>
            {/* <span className={clsx(styles.col1,styles.inputMain)}>
                 <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,styles.userInput,
                  errors?.firstName && styles.error
                )}
                id='firstName'
                type='text'
                register={register('firstName')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('firstName').onBlur(e);
                  handleInputBlur('firstName');
                }}
                onFocus={() => handleInputFocus('firstName')}
                errorInput={errors?.firstName}
              />
            </InputWrapper>
            </span>
          <span className={clsx(styles.col1,styles.inputMain)}>
                <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,styles.userInput,
                  errors?.lastName && styles.error
                )}
                id='lastName'
                type='text'
                register={register('lastName')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('lastName').onBlur(e);
                  handleInputBlur('lastName');
                }}
                onFocus={() => handleInputFocus('lastName')}
                errorInput={errors?.lastName}
              />
            </InputWrapper>

          </span> */}
            <span  className={styles.defaultInput} tabIndex={0} id='firstName'>{watch('firstName')}</span>
            <span  className={styles.defaultInput}>{watch('lastName')}</span>
          </span>
        </div>

        {/* YOUR EMAIL ADDRESS */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.email && styles.focusLbl)}
              htmlFor='email'
            >
              YOUR EMAIL ADDRESS
            </label>
          </span>
          <span className={styles.col1}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.email && styles.error
                )}
                type='email'
                register={register('email')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('email').onBlur(e);
                  handleInputBlur('email');
                  // handleEmailChange(e.currentTarget.value);
                }}
                onFocus={() => handleInputFocus('email')}
                errorInput={errors?.email}
              />
            </InputWrapper>
          </span>
        </div>

        {/* YOUR PASSWORD */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.password && styles.focusLbl)}
              htmlFor='password'
            >
              YOUR PASSWORD
            </label>
          </span>
          <span className={styles.col1}>
            <span
              onClick={changePassPopup}
              className={clsx(styles.inputCreateAccount, styles.changePassword)}
              tabIndex={0}
              onKeyDown={(e) => {
                if(e.key === 'Enter'){
                  changePassPopup();
                }
              }}
            >
              Change Password
            </span>
          </span>
        </div>

        {/* YOUR PHONE NUMBER */}
        <div className={clsx(styles.formGroupInput, styles.bdrBtm0)} data-hover-video-id="settings-user">
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.phoneNumber && styles.focusLbl)}
              htmlFor='phoneNumber'
            >
              YOUR PHONE NUMBER
            </label>
          </span>
          <span className={clsx(styles.col1, styles.inputMain)}>
          <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.phoneNumber && styles.error
                )}
                type='tel'
                register={register('phoneNumber')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('phoneNumber').onBlur(e);
                  handleInputBlur('phoneNumber');
                }}
                onFocus={() => handleInputFocus('phoneNumber')}
                errorInput={errors?.phoneNumber}
                mode='phoneNumber'
                lastFocusable="user-tab"
                onKeyDown={(e) => {
                  if (e.key === 'Tab') {
                    if(!e.shiftKey){
                      e.preventDefault();
                    const saveButton = document.getElementById('settings-save-button') as HTMLButtonElement;
                    if (saveButton) {
                      if (saveButton.disabled) {
                        const companyButton = document.getElementById('COMPANY')
                        if (companyButton) {
                          (companyButton as HTMLElement).focus();
                        }
                      } else {
                        setTimeout(() => {
                          saveButton.focus();
                        }, 0);
                      }
                    }
                  }
                  }
                }}
              />
            </InputWrapper>
          </span>
        </div>
      </div>
      <Dialog
        open={openChangePassPopup}
        onClose={(event) => setOpenChangePassPopup(false)}
        transitionDuration={100}
        container={routerContainerRef.current || changePassPopupRef.current}
        disableScrollLock={true}
        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          border: '1px solid transparent',
          borderRadius: '0px 0px 20px 20px',
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0,
          },
        }}
        hideBackdrop
        classes={{
          root: styles.changePassDialog,
          paper: styles.dialogContent,
        }}
      >
        <button
          className={styles.closeIcon}
          onClick={(event) => setOpenChangePassPopup(false)}
        >
          <CloseIcon />
        </button>
        <ChangePassword
          closeDialog={() => {
            setOpenChangePassPopup(false);
          }}
          deviceId={deviceId}
        />
      </Dialog>
      <Dialog
        open={showOtpDialog}
        onClose={handleOtpCancel}
        transitionDuration={100}
        container={routerContainerRef.current || changePassPopupRef.current}
        disableScrollLock={true}
        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          border: '1px solid transparent',
          borderRadius: '0px 0px 20px 20px',
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0,
          },
        }}
        hideBackdrop
        classes={{
          root: styles.changePassDialog,
          paper: styles.dialogContent,
        }}
      >
        <button
          className={styles.closeIcon}
          onClick={handleOtpCancel}
        >
          <CloseIcon />
        </button>
        
        <div className={styles.changePasswordWrapper}>
          <h2 className={styles.changePasswordTitle}>VERIFY YOUR EMAIL</h2>
          
          <div className={styles.changePasswordContent}>
            <p className={styles.verificationText}>
              We've sent a verification code to:
            </p>
            <p className={styles.emailDisplay}>{newEmail}</p>
            <p className={styles.instructionText}>
              Please enter the 6-digit code below:
            </p>
            
            <form onSubmit={otpHandleSubmit(handleOtpSubmit)}>
              <div className={styles.inputGroup}>
                <InputWrapper>
                  <CustomTextField 
                    register={otpRegister("otp")}
                    className={styles.otpInput}
                    onChange={(e: any) => {
                      e.target.value = e.target.value.replace(emojiRemoverRegex, '');
                      otpRegister("otp").onChange(e);
                    }}
                    placeholder='Enter 6-digit code' 
                    maxLength={6} 
                    mode="wholeNumber" 
                    errorInput={otpErrors.otp?.message}
                  />
                </InputWrapper>
              </div>
              
              <div className={styles.buttonGroup}>
                <button
                  type="button"
                  onClick={handleOtpCancel}
                  className={styles.cancelButton}
                >
                  CANCEL
                </button>
                <button
                  type="submit"
                  className={styles.verifyButton}
                >
                  VERIFY
                </button>
              </div>
            </form>
          </div>
        </div>
      </Dialog>
    </div>
  );
};

export default UserTab;
