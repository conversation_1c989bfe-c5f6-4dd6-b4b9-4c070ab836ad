import React, { useEffect, useState } from 'react';
import styles from './AttachmentBubble.module.scss';
import clsx from 'clsx';
import { ReactComponent as ExcelIcon } from '../../../assets/New-images/excel-table.svg';
import { ReactComponent as ImgIcon } from '../../../assets/New-images/upload-photo.svg';
import { ReactComponent as FileIcon } from '../../../assets/New-images/icon-file.svg';
import { Tooltip, Fade } from '@mui/material';
import PdfPreview from './PdfPreview';

interface AttachmentBubbleProps {
  name: string;
  extension: string;
  url: string;
  isMyMessage: boolean;
  isImgix: boolean;
  text?: string;
}

const AttachmentBubble: React.FC<AttachmentBubbleProps> = ({
  name,
  extension,
  url,
  isMyMessage,
  isImgix,
  text
}) => {
  const [showPreview, setShowPreview] = useState(false);
  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp',].includes(extension.toLowerCase());
  const isSvg = extension.toLowerCase() === 'svg';
  const isPdf = extension.toLowerCase() === 'pdf';
  const [imgixUrl, setImgixUrl] = useState('');

  useEffect(() => {
    if (isImgix && url) {
      const path = url.split('.com')[1];
      setImgixUrl(import.meta.env.VITE_CHAT_IMGIX_URL + path + import.meta.env.VITE_IMGIX_SUFFIX);
    }
  }, [isImgix, url]);

  // Function to determine file type icon based on extension
  const getFileTypeIcon = (extension: string) => {
    const ext = extension.toLowerCase();

    if (ext === 'pdf') {
        return (
          <div className={styles.fileIconContainer}>
            <div className={styles.pdfIcon}>
              <span className={styles.fileTypeText}><FileIcon/></span>
            </div>
          </div>
        );
      }

    // Common document types
    if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.documentIcon}>
            <span className={styles.fileTypeText}><FileIcon/></span>
          </div>
        </div>
      );
    }

    // Image types
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.imageIcon}>
            <ImgIcon/>
          </div>
        </div>
      );
    }

    // Spreadsheet types
    if (['xls', 'xlsx', 'csv'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.spreadsheetIcon}>
            <ExcelIcon/>
          </div>
        </div>
      );
    }

    // Presentation types
    if (['ppt', 'pptx'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.presentationIcon}>
            <span className={styles.fileTypeText}>PPT</span>
          </div>
        </div>
      );
    }

    // Archive types
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.archiveIcon}>
            <span className={styles.fileTypeText}>ZIP</span>
          </div>
        </div>
      );
    }

    // Default icon for unknown types
    return (
      <div className={styles.fileIconContainer}>
        <div className={styles.unknownIcon}>
          <span className={styles.fileTypeText}>FILE</span>
        </div>
      </div>
    );
  };

  // Image preview content for tooltip
  const ImagePreviewContent = () => (
    <div className={styles.imagePreviewContainer}>
      <img 
        src={isImgix?imgixUrl:url} 
        alt={name} 
        className={styles.imagePreview} 
      />
      <div className={styles.imagePreviewInfo}>
        <div className={styles.imagePreviewName}>{name}</div>
        <div className={styles.imagePreviewType}>{extension.toUpperCase()} Image</div>
      </div>
    </div>
  );

const SvgPreviewContent = () => {
    const [svgContent, setSvgContent] = useState<string>('');
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string>('');
  
    // Function to make SVG responsive with error handling
    const makeSvgResponsive = (svgText: string): string => {
      try {
        // Create a temporary DOM element to parse the SVG
        const parser = new DOMParser();
        const doc = parser.parseFromString(svgText, 'image/svg+xml');
        
        // Check for parsing errors
        const parserError = doc.querySelector('parsererror');
        if (parserError) {
          console.warn('SVG parsing error, using original content:', parserError.textContent);
          return svgText;
        }
        
        const svgElement = doc.querySelector('svg');
        
        // If no SVG element found, return original content
        if (!svgElement) {
          console.warn('No SVG element found in content, using original');
          return svgText;
        }
  
        // Safely remove hardcoded width and height attributes
        try {
          svgElement.removeAttribute('width');
          svgElement.removeAttribute('height');
        } catch (e) {
          console.warn('Could not remove width/height attributes:', e);
        }
        
        // Set responsive attributes with error handling
        try {
          svgElement.setAttribute('width', '100%');
          svgElement.setAttribute('height', '100%');
          svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');
        } catch (e) {
          console.warn('Could not set responsive attributes:', e);
        }
        
        // Handle viewBox with error handling
        try {
          if (!svgElement.getAttribute('viewBox')) {
            // Try to get dimensions from the original SVG
            const originalWidth = svgElement.getAttribute('width') || '100';
            const originalHeight = svgElement.getAttribute('height') || '100';
            
            // Extract numeric values safely
            const width = parseFloat(originalWidth.replace(/[^\d.]/g, '')) || 100;
            const height = parseFloat(originalHeight.replace(/[^\d.]/g, '')) || 100;
            
            svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
          }
        } catch (e) {
          console.warn('Could not set viewBox:', e);
        }
        
        // Add CSS styles with error handling
        try {
          if (svgElement.style) {
            svgElement.style.maxWidth = '100%';
            svgElement.style.maxHeight = '100%';
            svgElement.style.width = 'auto';
            svgElement.style.height = 'auto';
          }
        } catch (e) {
          console.warn('Could not set SVG styles:', e);
        }
        
        // Serialize back to string with error handling
        try {
          return new XMLSerializer().serializeToString(svgElement);
        } catch (e) {
          console.warn('Could not serialize SVG, using original content:', e);
          return svgText;
        }
        
      } catch (error) {
        console.warn('Error processing SVG, using original content:', error);
        return svgText;
      }
    };
  
    useEffect(() => {
      const fetchSvgContent = async () => {
        try {
          setIsLoading(true);
          setError('');
          
          const imageUrl = isImgix ? imgixUrl : url;
          const response = await fetch(imageUrl);
          
          if (!response.ok) {
            throw new Error(`Failed to fetch SVG: ${response.status}`);
          }
          
          const svgText = await response.text();
          
          // Validate that we got actual SVG content
          if (!svgText || !svgText.trim()) {
            throw new Error('Empty SVG content received');
          }
          
          // Check if it looks like SVG content
          if (!svgText.toLowerCase().includes('<svg')) {
            throw new Error('Content does not appear to be SVG');
          }
          
          // Make the SVG responsive before setting it
          const responsiveSvg = makeSvgResponsive(svgText);
          setSvgContent(responsiveSvg);
        } catch (err) {
          console.error('Error loading SVG:', err);
          setError(`Failed to load SVG preview: ${err instanceof Error ? err.message : 'Unknown error'}`);
        } finally {
          setIsLoading(false);
        }
      };
  
      fetchSvgContent();
    }, [url, isImgix, imgixUrl]);
  
    if (isLoading) {
      return (
        <div className={styles.imagePreviewContainer}>
          <div style={{ padding: '20px', textAlign: 'center' }}>
            Loading SVG...
          </div>
        </div>
      );
    }
  
    if (error) {
      return (
        <div className={styles.imagePreviewContainer}>
          <div style={{ 
            padding: '20px', 
            textAlign: 'center', 
            color: '#dc3545',
            background: '#f8d7da',
            border: '1px solid #f5c6cb',
            borderRadius: '4px',
            fontSize: '12px'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>⚠️ Preview Error</div>
            <div>{error}</div>
          </div>
          <div className={styles.imagePreviewInfo}>
            <div className={styles.imagePreviewName}>{name}</div>
            <div className={styles.imagePreviewType}>{extension.toUpperCase()} Vector Image</div>
          </div>
        </div>
      );
    }
  
    return (
      <div className={styles.imagePreviewContainer}>
        <div 
          style={{ 
            maxWidth: '100%', 
            maxHeight: '200px', 
            overflow: 'hidden',
            background: '#fff',
            padding: '4px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          dangerouslySetInnerHTML={{ __html: svgContent }}
        />
        <div className={styles.imagePreviewInfo}>
          <div className={styles.imagePreviewName}>{name}</div>
          <div className={styles.imagePreviewType}>{extension.toUpperCase()}</div>
        </div>
      </div>
    );
  };

  const PdfPreviewContent = () => (
    <PdfPreview url={url} name={name} extension={extension} />
  );

  const getPreviewContent = () => {
    if (isImage) {
      return <ImagePreviewContent />;
    } else if (isSvg) {
      return <SvgPreviewContent />;
    } else if (isPdf) {
      return <PdfPreviewContent />;
    }
    return null;
  };



  return (
    <div
      className={clsx(
        styles.attachmentBubble,
        !isMyMessage && styles.othersMessage
      )}
    >
      <div className={styles.attachmentCard}>
        {(isImage || isSvg) ? (
         
            <div className={styles.attachmentContent}>
              {getFileTypeIcon(extension)}
              <div className={styles.attachmentInfo}>
                <div className={styles.attachmentName}>{name}</div>
                <div className={styles.attachmentMeta}>
                  <span className={styles.attachmentExtension}>{extension}</span>
                </div>
              </div>
            </div>
        ) : (
          <div className={styles.attachmentContent}>
            {getFileTypeIcon(extension)}
            <div className={styles.attachmentInfo}>
              <div className={styles.attachmentName}>{name}</div>
              <div className={styles.attachmentMeta}>
                <span className={styles.attachmentExtension}>{extension}</span>
              </div>
            </div>
          </div>
        )}

        <div className={styles.attachmentActions}>
            {(isImage || isPdf || isSvg) &&  <Tooltip
            title={getPreviewContent()}
            placement="top-start"
            TransitionComponent={Fade}
            TransitionProps={{ timeout: 200 }}
            classes={{
              tooltip: styles.imageTooltip,
            }}
            onOpen={() => setShowPreview(true)}
            onClose={() => setShowPreview(false)}
          >
            <span className={styles.previewButton}>Preview</span>
          </Tooltip>}
          
          <a
            href={url}
            title='Download'
            target="_blank"
            rel="noopener noreferrer"
            className={styles.downloadButton}
          >
            <div className={styles.downloadIcon}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 15L7 10H10V3H14V10H17L12 15Z" fill="currentColor"/>
                <path d="M20 18H4V20H20V18Z" fill="currentColor"/>
              </svg>
            </div>
          </a>
        </div>

        {text ? (
          <div className={styles.attachmentText}>
            <div dangerouslySetInnerHTML={{ __html: text }} />
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default AttachmentBubble;
