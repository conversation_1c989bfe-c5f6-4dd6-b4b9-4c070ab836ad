import { create } from 'zustand';

interface DeletedItems {
  INSTANT_PRICING: any[];
  PO: any[];
  QUOTE: any[];
}

interface DeletedItemsStore {
  deletedItems: DeletedItems;
  setDeletedItems: (deletedItems: DeletedItems) => void;
  staticDeletedItems: any[];
  setStaticDeletedItems: (staticDeletedItems: any[]) => void;
}

export const useDeletedItemsStore = create<DeletedItemsStore>((set) => ({
  deletedItems: {
    INSTANT_PRICING: [],
    PO: [],
    QUOTE: [],
  },
  setDeletedItems: (deletedItems) => set({ deletedItems }),
  staticDeletedItems: [],
  setStaticDeletedItems: (staticDeletedItems) => set({ staticDeletedItems }),
}));
